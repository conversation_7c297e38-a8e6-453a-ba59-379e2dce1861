import discord
from discord.ext import commands
import os
from dotenv import load_dotenv
import asyncio

# Load environment variables
load_dotenv()

# Bot configuration
TOKEN = os.getenv('DISCORD_BOT_TOKEN')
CLIENT_ID = os.getenv('DISCORD_CLIENT_ID')

if not TOKEN:
    print("❌ Error: DISCORD_BOT_TOKEN not found in environment variables!")
    print("Please create a .env file with your bot token.")
    exit(1)

if not CLIENT_ID:
    print("❌ Error: DISCORD_CLIENT_ID not found in environment variables!")
    print("Please create a .env file with your client ID.")
    exit(1)

# Create bot instance with minimal intents (no privileged intents required)
intents = discord.Intents.default()
intents.guilds = True  # Required for slash commands

bot = commands.Bot(command_prefix='!', intents=intents)

@bot.event
async def on_ready():
    """Event triggered when bot is ready"""
    print(f"✅ Bot is ready! Logged in as {bot.user}")
    print(f"🤖 Bot ID: {bot.user.id}")
    print(f"📊 Serving {len(bot.guilds)} servers")

    # Sync slash commands
    try:
        synced = await bot.tree.sync()
        print(f"🔄 Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"❌ Failed to sync commands: {e}")

@bot.tree.command(name="test", description="Test if the bot is working")
async def test_command(interaction: discord.Interaction):
    """Simple test command to verify bot functionality"""
    try:
        await interaction.response.send_message(
            "✅ Bot is working! Commands are functioning properly.",
            ephemeral=False
        )
        print(f"✅ /test command executed by {interaction.user}")
    except Exception as e:
        print(f"❌ Error in test command: {e}")
        if not interaction.response.is_done():
            await interaction.response.send_message(f"❌ Error: {e}", ephemeral=True)

@bot.tree.command(name="say", description="Make the bot say something")
async def say_command(interaction: discord.Interaction, message: str):
    """
    Slash command that makes the bot repeat a message

    Parameters:
    - message: The message you want the bot to say (required)
    """
    try:
        # Respond with the exact message content (explicitly set as public)
        await interaction.response.send_message(
            content=message,
            ephemeral=False  # Explicitly make it public/visible to everyone
        )

        # Log the command usage
        print(f"✅ /say command executed by {interaction.user}: \"{message}\" - Response sent publicly")

    except Exception as e:
        print(f"❌ Error handling /say command: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        print(f"❌ User: {interaction.user}, Guild: {interaction.guild}")

        # Try to respond with an error message if we haven't responded yet
        if not interaction.response.is_done():
            await interaction.response.send_message(
                f"❌ Sorry, there was an error processing your command: {str(e)}",
                ephemeral=True
            )

@bot.event
async def on_error(event, *args, **kwargs):
    """Handle bot errors"""
    print(f"❌ Bot error in {event}: {args}")

@bot.event
async def on_command_error(ctx, error):
    """Handle command errors"""
    print(f"❌ Command error: {error}")

# Run the bot
if __name__ == "__main__":
    try:
        bot.run(TOKEN)
    except discord.LoginFailure:
        print("❌ Failed to login to Discord. Check your bot token.")
    except Exception as e:
        print(f"❌ An error occurred: {e}")